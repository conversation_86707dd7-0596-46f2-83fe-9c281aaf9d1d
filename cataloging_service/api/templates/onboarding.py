# Property Onboarding and Utility API
"""
Property Onboarding and Utility operations.

This module handles:
- Property onboarding with template-based entity creation
- Health check endpoint
- Other utility operations
"""

import logging
from flask import Blueprint, request
from pydantic import ValidationError

from cataloging_service.common.api_responses import ApiResponse, api_endpoint
from cataloging_service.common.request_decorators import parse_body
from cataloging_service.schemas.transaction_schemas import (
    PropertyOnboardingRequestSchema
)
from cataloging_service.domain.services.property.property_onboarding_service import PropertyOnboardingService
from object_registry import locate_instance, inject

bp = Blueprint("template_onboarding", __name__)
logger = logging.getLogger(__name__)


@bp.route("/property-onboarding", methods=["POST"])
@inject(property_onboarding_service=PropertyOnboardingService)
@parse_body(PropertyOnboardingRequestSchema)
@api_endpoint
def onboard_property(
        data: PropertyOnboardingRequestSchema,
        property_onboarding_service: PropertyOnboardingService,
):
    """
    Onboard a property with template-based entity creation
    ---
    tags:
      - Template Management
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/PropertyOnboardingRequestSchema'
    responses:
      201:
        description: Property onboarded successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyOnboardingResponseSchema'
      400:
        description: Validation error
      404:
        description: Property not found
    """
    try:
        onboarding_result = property_onboarding_service.onboard_property(
            data
        )
        return ApiResponse.created(onboarding_result)

    except ValidationError as e:
        logger.warning(f"Validation error in property onboarding: {e}")
        return ApiResponse.validation_error(e)
    except ValueError as e:
        logger.warning(f"Business logic error in property onboarding: {e}")
        return ApiResponse.validation_error(ValidationError(str(e)))
    except Exception as e:
        logger.exception(f"Unexpected error in property onboarding: {e}")
        return ApiResponse.server_error(e, include_details=False)
