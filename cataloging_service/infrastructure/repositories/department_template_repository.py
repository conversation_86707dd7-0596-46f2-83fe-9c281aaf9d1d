"""
Department Template Repository
Repository implementation for department template operations following existing pattern.
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.department_template_adapter import (
    DepartmentTemplateAdapter,
)
from cataloging_service.domain.entities.templates.department_template import (
    DepartmentTemplateEntity,
)
from cataloging_service.models import DepartmentTemplate


@register_instance()
class DepartmentTemplateRepository(BaseEntityRepository):
    """Repository for department template operations"""

    adaptor = DepartmentTemplateAdapter()

    def create(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Create a new department template"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(self, template_id: int) -> Optional[DepartmentTemplateEntity]:
        """Get department template by ID"""
        model = self.rget_by_attr(DepartmentTemplate, limit_one=True, id=template_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand_and_code(
        self, brand_id: int, code: str
    ) -> Optional[DepartmentTemplateEntity]:
        """Get department template by brand ID and code"""
        model = self.rget_by_attr(
            DepartmentTemplate, limit_one=True, brand_id=brand_id, code=code
        )
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand(
        self, brand_id: int, active_only: bool = True
    ) -> List[DepartmentTemplateEntity]:
        """Get all department templates for a brand"""
        filters = {"brand_id": brand_id}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(DepartmentTemplate, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Update department template"""
        existing_model = self.rget_by_attr(
            DepartmentTemplate, limit_one=True, id=entity.id
        )
        if not existing_model:
            raise ValueError(f"Department template with ID {entity.id} not found")

        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, template_id: int) -> bool:
        """Delete department template"""
        model = self.rget_by_attr(DepartmentTemplate, limit_one=True, id=template_id)
        if not model:
            return False

        self.delete(model)
        return True

    def get_auto_create_templates(
        self, brand_id: int
    ) -> List[DepartmentTemplateEntity]:
        """Get templates that should be auto-created on property launch"""
        models = self.rget_by_attr(
            DepartmentTemplate,
            brand_id=brand_id,
            is_active=True,
            auto_create_on_property_launch=True,
        )
        return [self.adaptor.to_entity(model) for model in models]

    def exists_by_brand_and_code(
        self, brand_id: int, code: str, exclude_id: Optional[int] = None
    ) -> bool:
        """Check if department template exists with given brand and code"""
        query = (
            self.session()
            .query(DepartmentTemplate)
            .filter_by(brand_id=brand_id, code=code)
        )
        if exclude_id:
            query = query.filter(DepartmentTemplate.id != exclude_id)
        return query.first() is not None

    def get_all(self, active_only: bool = True) -> List[DepartmentTemplateEntity]:
        """Get all department templates"""
        filters = {}
        if active_only:
            filters["is_active"] = True

        models = self.rget_by_attr(DepartmentTemplate, **filters)
        return [self.adaptor.to_entity(model) for model in models]

    def get_by_codes(self, codes: List[str]) -> List[DepartmentTemplateEntity]:
        """Get department templates by codes"""
        models = self.rget_by_attr(DepartmentTemplate, code=codes)
        return [self.adaptor.to_entity(model) for model in models]

    def get_children(
        self, parent_code: str, brand_id: int
    ) -> List[DepartmentTemplateEntity]:
        """Get child department templates for a parent"""
        models = self.rget_by_attr(
            DepartmentTemplate,
            parent_code=parent_code,
            brand_id=brand_id,
            is_active=True,
        )
        return [self.adaptor.to_entity(model) for model in models]

    def get_root_departments(self, brand_id: int) -> List[DepartmentTemplateEntity]:
        """Get root department templates (no parent)"""
        query = (
            self.session()
            .query(DepartmentTemplate)
            .filter_by(brand_id=brand_id, is_active=True, parent_code=None)
        )
        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def get_with_filters(self, filters) -> List[DepartmentTemplateEntity]:
        """Get department templates with generic filters

        Supported filters:
        - brand_id: Filter by brand ID
        - parent_code: Filter by parent department code
        - is_active: Filter by active status (default: True if not specified)
        - auto_create_on_property_launch: Filter by auto-create flag
        """
        query = self.session().query(DepartmentTemplate)

        # Apply filters
        if filters.get("brand_id"):
            query = query.filter(DepartmentTemplate.brand_id == filters.get("brand_id"))

        if filters.get("parent_code"):
            query = query.filter(
                DepartmentTemplate.parent_code == filters.get("parent_code")
            )

        # Default to active only if not explicitly specified
        is_active = filters.get("is_active", True)
        if is_active is not None:
            query = query.filter(DepartmentTemplate.is_active == is_active)

        if filters.get("auto_create_on_property_launch") is not None:
            query = query.filter(
                DepartmentTemplate.auto_create_on_property_launch
                == filters.get("auto_create_on_property_launch")
            )

        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]
