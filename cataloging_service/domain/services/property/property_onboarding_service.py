# Property Onboarding Service
"""
Property Onboarding business logic service.

This service handles the complete property onboarding process including:
- Property validation and brand resolution
- Template-based entity creation (departments, profit centers, SKUs)
- Transaction code generation
- Error handling and rollback
"""

import logging
from datetime import datetime
from typing import List, Dict, Any

from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)

from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.property.property_profit_center_service import (
    PropertyProfitCenterService,
)
from cataloging_service.domain.services.property.property_sku_service import (
    PropertySkuService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.domain.services.template.sku_template_service import (
    SkuTemplateService,
)
from cataloging_service.domain.services.template.template_filter import TemplateFilter
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.schemas.transaction_schemas import (
    PropertyOnboardingRequestSchema,
    PropertyOnboardingResponseSchema,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        SkuTemplateService,
        PropertyDepartmentService,
        PropertyProfitCenterService,
        PropertySkuService,
    ]
)
class PropertyOnboardingService(object):
    def __init__(
        self,
        department_template_service,
        profit_center_template_service,
        sku_template_service,
        property_department_service,
        property_profit_center_service,
        property_sku_service,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.sku_template_service = sku_template_service
        self.property_department_service = property_department_service
        self.property_profit_center_service = property_profit_center_service
        self.property_sku_service = property_sku_service

        # Access PropertyService through service provider (existing pattern)
        from cataloging_service.domain import service_provider
        self.property_service = service_provider.property_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        """
        Execute complete property onboarding process

        Args:
            request: Property onboarding request with property_id and configuration

        Returns:
            PropertyOnboardingResponseSchema: Onboarding results and statistics

        Raises:
            ValueError: If property not found or invalid brand
            Exception: For other onboarding errors
        """
        logger.info(
            f"Starting property onboarding for property_id: {request.property_id}"
        )

        # Initialize onboarding entity to track progress
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,  # Will be validated/resolved from property
            template_filters=request.custom_config or {},
        )

        try:
            # Step 1: Validate property and resolve brand
            property_obj, resolved_brand_id = self._validate_property_and_resolve_brand(
                request.property_id, request.brand_id
            )
            onboarding_entity.brand_id = resolved_brand_id

            # Step 2: Create departments from templates
            if request.auto_create_departments:
                departments_created = self._create_departments_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.departments_created = len(departments_created)
                onboarding_entity.created_entities["departments"] = departments_created
                logger.info(
                    f"Created {len(departments_created)} departments for property {request.property_id}"
                )

            # Step 3: Create profit centers from templates
            if request.auto_create_profit_centers:
                profit_centers_created = self._create_profit_centers_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.profit_centers_created = len(profit_centers_created)
                onboarding_entity.created_entities["profit_centers"] = (
                    profit_centers_created
                )
                logger.info(
                    f"Created {len(profit_centers_created)} profit centers for property {request.property_id}"
                )

            # Step 4: Create SKUs from templates
            if request.auto_create_skus:
                skus_created = self._create_skus_from_templates(
                    onboarding_entity, request.custom_config
                )
                onboarding_entity.skus_created = len(skus_created)
                onboarding_entity.created_entities["skus"] = skus_created
                logger.info(
                    f"Created {len(skus_created)} SKUs for property {request.property_id}"
                )

            # Step 5: Generate transaction codes
            if request.generate_transaction_codes:
                transaction_codes = self._generate_transaction_codes(onboarding_entity)
                onboarding_entity.transaction_codes_generated = len(transaction_codes)
                onboarding_entity.created_entities["transaction_codes"] = (
                    transaction_codes
                )
                logger.info(
                    f"Generated {len(transaction_codes)} transaction codes for property {request.property_id}"
                )

            # Mark as completed
            onboarding_entity.mark_completed()

            logger.info(
                f"Property onboarding completed successfully for property_id: {request.property_id}"
            )

            # Return response
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.utcnow(),
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )

            # Return partial results with error information
            return PropertyOnboardingResponseSchema(
                property_id=onboarding_entity.property_id,
                brand_id=onboarding_entity.brand_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                skus_created=onboarding_entity.skus_created,
                transaction_codes_generated=onboarding_entity.transaction_codes_generated,
                onboarding_status=onboarding_entity.onboarding_status,
                created_entities=onboarding_entity.created_entities,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=datetime.utcnow(),
            )

    def _validate_property_and_resolve_brand(
        self, property_id: str, brand_id: int
    ) -> tuple:
        """
        Validate property exists and resolve brand_id

        Args:
            property_id: Property ID to validate
            brand_id: Brand ID from request (for validation)

        Returns:
            tuple: (property_object, resolved_brand_id)

        Raises:
            ValueError: If property not found or brand mismatch
        """
        # Fetch property
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")

        # Get property's brand(s) - properties can have multiple brands
        # property_brands = getattr(property_obj, "brands", [])
        # if not property_brands:
        #     raise ValueError(f"Property '{property_id}' has no associated brands")

        # Validate provided brand_id matches one of property's brands
        # property_brand_ids = [brand.id for brand in property_brands]
        # if brand_id not in property_brand_ids:
        #     raise ValueError(
        #         f"Brand ID {brand_id} does not match property's brands: {property_brand_ids}"
        #     )

        logger.info(f"Property {property_id} validated with brand_id {brand_id}")
        return property_obj, brand_id

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        """Create property departments from brand templates"""
        created_departments = []

        try:
            # Get department templates for the brand
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )

            # Apply custom filters if provided
            if custom_config and "department_filters" in custom_config:
                for key, value in custom_config["department_filters"].items():
                    template_filter.filters[key] = value

            department_templates = (
                self.department_template_service.get_department_templates(
                    template_filter
                )
            )

            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_departments

            # Create departments from templates
            for template in department_templates:
                try:
                    # Convert template to property department entity
                    department_entity = self._convert_department_template_to_entity(
                        template, onboarding_entity.property_id
                    )

                    # Create the department
                    created_department = (
                        self.property_department_service.create_property_department(
                            department_entity
                        )
                    )
                    created_departments.append(created_department.code)

                except Exception as e:
                    error_msg = f"Failed to create department from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_departments

    def _create_profit_centers_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        """Create property profit centers from brand templates"""
        created_profit_centers = []

        try:
            # Get profit center templates for the brand
            template_filter = TemplateFilter(
                brand_id=onboarding_entity.brand_id,
                is_active=True,
                auto_create_on_property_launch=True,
            )

            # Apply custom filters if provided
            if custom_config and "profit_center_filters" in custom_config:
                for key, value in custom_config["profit_center_filters"].items():
                    template_filter.filters[key] = value

            profit_center_templates = (
                self.profit_center_template_service.get_profit_center_templates(
                    template_filter
                )
            )

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_profit_centers

            # Create profit centers from templates
            for template in profit_center_templates:
                try:
                    # Convert template to property profit center entity
                    profit_center_entity = (
                        self._convert_profit_center_template_to_entity(
                            template, onboarding_entity.property_id
                        )
                    )

                    # Create the profit center
                    created_profit_center = self.property_profit_center_service.create_property_profit_center(
                        profit_center_entity
                    )
                    created_profit_centers.append(created_profit_center.code)

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_profit_centers

    def _create_skus_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity, custom_config: Dict[str, Any]
    ) -> List[str]:
        """Create property SKUs from brand templates"""
        created_skus = []

        try:
            # Get SKU templates for the brand (via department templates)
            template_filter = TemplateFilter(
                is_active=True, auto_create_on_property_launch=True
            )

            # Apply custom filters if provided
            if custom_config and "sku_filters" in custom_config:
                for key, value in custom_config["sku_filters"].items():
                    template_filter.filters[key] = value

            sku_templates = self.sku_template_service.get_sku_templates(template_filter)

            # Filter SKU templates by brand (through department template association)
            brand_sku_templates = []
            for template in sku_templates:
                # Get the department template to check brand
                dept_filter = TemplateFilter(
                    brand_id=onboarding_entity.brand_id,
                    code=template.department_template_code,
                    is_active=True,
                )
                dept_templates = (
                    self.department_template_service.get_department_templates(
                        dept_filter
                    )
                )
                if dept_templates:
                    brand_sku_templates.append(template)

            if not brand_sku_templates:
                onboarding_entity.add_warning(
                    "No SKU templates found for auto-creation"
                )
                return created_skus

            # Create SKUs from templates
            for template in brand_sku_templates:
                try:
                    # Convert template to property SKU entity
                    sku_entity = self._convert_sku_template_to_entity(
                        template, onboarding_entity.property_id
                    )

                    # Create the SKU
                    created_sku = self.property_sku_service.create_property_sku(
                        sku_entity
                    )
                    created_skus.append(created_sku.code)

                except Exception as e:
                    error_msg = (
                        f"Failed to create SKU from template {template.code}: {str(e)}"
                    )
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create SKUs from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return created_skus

    def _generate_transaction_codes(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> List[str]:
        """Generate transaction codes for created entities"""
        transaction_codes = []

        try:
            # Generate codes based on created entities
            # This is a simplified implementation - actual logic would depend on business requirements

            # Generate codes for departments
            for dept_code in onboarding_entity.created_entities.get("departments", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{dept_code}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            # Generate codes for profit centers
            for pc_code in onboarding_entity.created_entities.get("profit_centers", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{pc_code}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            # Generate codes for SKUs
            for sku_code in onboarding_entity.created_entities.get("skus", []):
                tx_code = f"TXN_{onboarding_entity.property_id}_{sku_code}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
                transaction_codes.append(tx_code)

            if not transaction_codes:
                onboarding_entity.add_warning(
                    "No transaction codes generated - no entities were created"
                )

        except Exception as e:
            error_msg = f"Failed to generate transaction codes: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)

        return transaction_codes

    def _convert_department_template_to_entity(self, template, property_id: str):
        """Convert department template to property department entity"""
        # Import here to avoid circular imports
        from cataloging_service.domain.entities.properties.property_department import (
            PropertyDepartmentEntity,
        )

        return PropertyDepartmentEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            parent_id=None,  # Will need to resolve parent_id from parent_code if needed
            description=template.description,
            financial_code=template.financial_code,
            is_active=template.is_active,
            is_custom=False,  # Created from template, not custom
            created_from_template_code=template.code,
            config=template.template_config or {},
        )

    def _convert_profit_center_template_to_entity(self, template, property_id: str):
        """Convert profit center template to property profit center entity"""
        # Import here to avoid circular imports
        from cataloging_service.domain.entities.properties.property_profit_center import (
            PropertyProfitCenterEntity,
        )

        # Resolve department_id from department_template_code
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )

        return PropertyProfitCenterEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            description=template.description,
            department_id=department_id,
            is_active=template.is_active,
            is_auto_created=True,  # Created from template
            is_custom=False,
            created_from_template_code=template.code,
            config=template.template_config or {},
        )

    def _convert_sku_template_to_entity(self, template, property_id: str):
        """Convert SKU template to property SKU entity"""
        # Import here to avoid circular imports
        from cataloging_service.domain.entities.properties.property_sku import (
            PropertySkuEntity,
        )

        # Resolve department_id and profit_center_id from template codes
        department_id = self._resolve_department_id_from_template_code(
            property_id, template.department_template_code
        )
        profit_center_id = self._resolve_profit_center_id_from_template_code(
            property_id, template.profit_center_template_code
        )

        return PropertySkuEntity(
            property_id=property_id,
            code=template.code,
            name=template.name,
            display_name=template.display_name,
            description=template.description,
            department_id=department_id,
            profit_center_id=profit_center_id,
            sku_category_id=template.category_id,
            default_list_price=template.default_list_price,
            default_sale_price=template.default_sale_price,
            hsn_sac=template.hsn_sac,
            tax_at_room_rate=template.tax_at_room_rate,
            is_active=template.is_active,
            is_custom=False,  # Created from template, not custom
            is_modular=template.is_modular,
            saleable=template.saleable,
            chargeable_per_occupant=template.chargeable_per_occupant,
            is_property_inclusion=template.is_property_inclusion,
            created_from_template_code=template.code,
            config=template.template_config or {},
            sku_details=template.sku_details or {},
        )

    def _resolve_department_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> int:
        """Resolve department ID from template code for a specific property"""
        try:
            # Get departments for the property that were created from the template
            departments = self.property_department_service.get_property_departments(
                property_id, active_only=False
            )

            for dept in departments:
                if dept.created_from_template_code == template_code:
                    return dept.id

            # If not found, this might be a new department that needs to be created first
            # For now, raise an error - in a real implementation, this would trigger department creation
            raise ValueError(
                f"Department with template code '{template_code}' not found for property {property_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to resolve department ID for template code {template_code}: {e}"
            )
            # Return a default value to prevent complete failure
            return 1

    def _resolve_profit_center_id_from_template_code(
        self, property_id: str, template_code: str
    ) -> str:
        """Resolve profit center ID from template code for a specific property"""
        try:
            # Get profit centers for the property that were created from the template
            profit_centers = (
                self.property_profit_center_service.get_property_profit_centers(
                    property_id, active_only=False
                )
            )

            for pc in profit_centers:
                if pc.created_from_template_code == template_code:
                    return pc.id

            # If not found, this might be a new profit center that needs to be created first
            # For now, raise an error - in a real implementation, this would trigger profit center creation
            raise ValueError(
                f"Profit center with template code '{template_code}' not found for property {property_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to resolve profit center ID for template code {template_code}: {e}"
            )
            # Return a default value to prevent complete failure
            return "PC001"
