# Property Profit Center Service
"""
Property Profit Center business logic service.

This service handles all business operations for property profit centers including:
- Creation with business validation
- Updates with department association validation
- Retrieval
- Deletion with dependency checks
"""

from typing import List, Optional

from object_registry import register_instance
from cataloging_service.domain.entities.properties.property_profit_center import (
    PropertyProfitCenterEntity,
)
from cataloging_service.infrastructure.repositories.property_profit_center_repository import (
    PropertyProfitCenterRepository,
)
from cataloging_service.infrastructure.repositories.property_department_repository import (
    PropertyDepartmentRepository,
)


@register_instance(
    dependencies=[PropertyProfitCenterRepository, PropertyDepartmentRepository]
)
class PropertyProfitCenterService:
    """Service for property profit center business logic"""

    def __init__(self, repository, department_repository):
        self.repository = repository
        self.department_repository = department_repository

    def create_property_profit_center(
        self, entity: PropertyProfitCenterEntity
    ) -> PropertyProfitCenterEntity:
        """Create a new property profit center with business validation"""
        # Business rule: Check for duplicate code within property
        if self.repository.exists_by_property_and_code(entity.property_id, entity.code):
            raise ValueError(
                f"Profit center with code '{entity.code}' already exists for property {entity.property_id}"
            )

        # Business rule: Validate department association
        department = self.department_repository.get_by_id(entity.department_id)
        if not department:
            raise ValueError(f"Department with ID {entity.department_id} not found")
        if department.property_id != entity.property_id:
            raise ValueError("Department must belong to the same property")
        if not department.is_active:
            raise ValueError("Cannot create profit center for inactive department")

        return self.repository.create(entity)

    def update_property_profit_center(
        self, entity: PropertyProfitCenterEntity
    ) -> PropertyProfitCenterEntity:
        """Update property profit center with business validation"""
        # Business rule: Check for duplicate code within property (excluding current)
        if self.repository.exists_by_property_and_code(
            entity.property_id, entity.code, exclude_id=entity.id
        ):
            raise ValueError(
                f"Profit center with code '{entity.code}' already exists for property {entity.property_id}"
            )

        # Business rule: Validate department association
        department = self.department_repository.get_by_id(entity.department_id)
        if not department:
            raise ValueError(f"Department with ID {entity.department_id} not found")
        if department.property_id != entity.property_id:
            raise ValueError("Department must belong to the same property")

        return self.repository.update(entity)

    def get_property_profit_center_by_id(
        self, profit_center_id: str
    ) -> Optional[PropertyProfitCenterEntity]:
        """Get property profit center by ID"""
        return self.repository.get_by_id(profit_center_id)

    def get_property_profit_centers(
        self, property_id: str, active_only: bool = True
    ) -> List[PropertyProfitCenterEntity]:
        """Get all profit centers for a property"""
        return self.repository.get_by_property(property_id, active_only)

    def get_profit_centers_by_department(
        self, department_id: int
    ) -> List[PropertyProfitCenterEntity]:
        """Get profit centers for a specific department"""
        return self.repository.get_by_department(department_id)

    def delete_property_profit_center(self, profit_center_id: str) -> bool:
        """Delete property profit center with business validation"""
        profit_center = self.repository.get_by_id(profit_center_id)
        if not profit_center:
            return False

        # Business rule: Check if profit center has dependent SKUs
        # This would require checking SKUs - simplified for now
        # skus = self.sku_repository.get_by_profit_center(profit_center_id)
        # if not profit_center.can_be_deleted(skus):
        #     raise ValueError("Cannot delete profit center with active SKUs")

        return self.repository.delete(profit_center_id)
