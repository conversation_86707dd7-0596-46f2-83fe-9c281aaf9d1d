# Template Filter Utility
"""
Generic filter class for template queries that works with Pydantic schemas.

This utility provides a consistent way to handle filtering across all template services.
"""

from typing import Any


class TemplateFilter:
    """Generic filter class for template queries that works with Pydantic schemas"""

    def __init__(self, schema_instance=None, **filters):
        if schema_instance:
            # Extract non-None values from Pydantic schema
            self.filters = {
                k: v for k, v in schema_instance.model_dump(exclude_none=True).items()
            }
        else:
            # Fallback to kwargs for backward compatibility
            self.filters = {k: v for k, v in filters.items() if v is not None}

    @classmethod
    def from_schema(cls, schema_instance):
        """Create filter from Pydantic schema instance"""
        return cls(schema_instance=schema_instance)

    def is_empty(self) -> bool:
        """Check if filter has no criteria"""
        return len(self.filters) == 0

    def get(self, key: str, default=None) -> Any:
        """Get filter value by key"""
        return self.filters.get(key, default)

    def __repr__(self):
        return f"TemplateFilter({self.filters})"
