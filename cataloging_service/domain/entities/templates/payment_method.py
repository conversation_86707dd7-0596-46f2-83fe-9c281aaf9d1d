"""
Payment Method Domain Entity
Top-level payment method with business logic and validation.
"""

from datetime import datetime
from typing import Optional, Dict, Any

from pydantic import Field, field_validator
from pydantic.types import PositiveInt

from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class PaymentMethodEntity(BaseDomainEntity):
    """Top-level payment method domain entity"""

    id: Optional[PositiveInt] = None
    name: str = Field(
        ..., min_length=1, max_length=255, description="Payment method name"
    )
    code: str = Field(
        ..., min_length=2, max_length=50, description="Unique payment method code"
    )
    payment_method_type: str = Field(
        ..., max_length=50, description="Payment method type"
    )
    paid_to: str = Field(..., max_length=50, description="Who receives the payment")
    allowed_paid_by: str = Field(
        ..., max_length=50, description="Who can make the payment"
    )
    flow_direction: str = Field(
        "INFLOW",
        max_length=20,
        description="Payment flow direction (INFLOW for payments, OUTFLOW for refunds)",
    )
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Payment method configuration"
    )
    is_active: bool = Field(True, description="Whether payment method is active")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("code")
    @classmethod
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError(
                "Code must contain only alphanumeric characters, hyphens, and underscores"
            )
        return v.upper()

    def supports_gateway(self) -> bool:
        """Business rule: Check if payment method supports gateway integration"""
        return "gateway" in self.config and self.config["gateway"] is not None

    def requires_verification(self) -> bool:
        """Business rule: Check if payment method requires verification"""
        return self.config.get("requires_verification", False)

    def is_inflow(self) -> bool:
        """Business rule: Check if payment method is for inflow (payments)"""
        return self.flow_direction == "INFLOW"

    def is_outflow(self) -> bool:
        """Business rule: Check if payment method is for outflow (refunds)"""
        return self.flow_direction == "OUTFLOW"

    def can_process_refunds(self) -> bool:
        """Business rule: Check if payment method can process refunds"""
        return self.flow_direction == "OUTFLOW" or self.config.get(
            "supports_refunds", False
        )
